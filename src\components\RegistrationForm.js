'use client';

import { useState } from 'react';
import { submitProject } from '../lib/supabase';

const RegistrationForm = ({ isOpen, onClose, isExpired = false }) => {
  const [formData, setFormData] = useState({
    teamName: '',
    experience: '',
    projectIdea: '',
    urls: {
      github: '',
      googleDrive: '',
      figma: ''
    },
    additionalUrls: [],
    files: [],
    customFields: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };



  const handleUrlChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      urls: {
        ...prev.urls,
        [field]: value
      }
    }));
  };

  const addAdditionalUrl = () => {
    setFormData(prev => ({
      ...prev,
      additionalUrls: [...prev.additionalUrls, { label: '', url: '' }]
    }));
  };

  const updateAdditionalUrl = (index, field, value) => {
    const newUrls = [...formData.additionalUrls];
    newUrls[index][field] = value;
    setFormData(prev => ({
      ...prev,
      additionalUrls: newUrls
    }));
  };

  const removeAdditionalUrl = (index) => {
    setFormData(prev => ({
      ...prev,
      additionalUrls: prev.additionalUrls.filter((_, i) => i !== index)
    }));
  };

  const addFileUpload = () => {
    setFormData(prev => ({
      ...prev,
      files: [...prev.files, { label: '', file: null }]
    }));
  };

  const updateFileUpload = (index, field, value) => {
    const newFiles = [...formData.files];
    newFiles[index][field] = value;
    setFormData(prev => ({
      ...prev,
      files: newFiles
    }));
  };

  const removeFileUpload = (index) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  };

  const addCustomField = () => {
    setFormData(prev => ({
      ...prev,
      customFields: [...prev.customFields, { label: '', value: '', type: 'text' }]
    }));
  };

  const updateCustomField = (index, field, value) => {
    const newFields = [...formData.customFields];
    newFields[index][field] = value;
    setFormData(prev => ({
      ...prev,
      customFields: newFields
    }));
  };

  const removeCustomField = (index) => {
    setFormData(prev => ({
      ...prev,
      customFields: prev.customFields.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare data for submission
      const submissionData = {
        team_name: formData.teamName,
        experience: formData.experience,
        project_description: formData.projectIdea,
        urls: {
          ...formData.urls,
          additional: formData.additionalUrls
        },
        files: formData.files,
        custom_fields: formData.customFields
      };

      const { data, error } = await submitProject(submissionData);

      if (error) {
        throw error;
      }

      console.log('Project submitted successfully:', data);
      alert('Project submitted successfully!');
      onClose();
    } catch (error) {
      console.error('Error submitting project:', error);
      alert('Error submitting project. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">

        {isExpired && (
          <div className="bg-red-600/20 border border-red-500/50 rounded-lg p-4 mb-6 text-center">
            <h3 className="text-red-200 text-lg font-bold mb-2">Submission Period Ended</h3>
            <p className="text-red-300">The deadline has passed and new submissions are no longer being accepted.</p>
          </div>
        )}
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">Project Submission</h2>
          <button
            onClick={onClose}
            className="text-white/60 hover:text-white text-2xl font-bold"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Team Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Team Information</h3>

            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">Team Name *</label>
              <input
                type="text"
                required
                value={formData.teamName}
                onChange={(e) => handleInputChange('teamName', e.target.value)}
                className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your team name"
              />
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Additional Information</h3>
            
            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">Experience Level</label>
              <select
                value={formData.experience}
                onChange={(e) => handleInputChange('experience', e.target.value)}
                className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select experience level</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>

            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">Project Description *</label>
              <textarea
                required
                value={formData.projectIdea}
                onChange={(e) => handleInputChange('projectIdea', e.target.value)}
                rows={4}
                className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="Describe your project, technologies used, and key features..."
              />
            </div>
          </div>

          {/* Project URLs */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Project URLs</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-white/80 text-sm font-medium mb-2">GitHub Repository</label>
                <input
                  type="url"
                  value={formData.urls.github}
                  onChange={(e) => handleUrlChange('github', e.target.value)}
                  className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://github.com/username/repo"
                />
              </div>

              <div>
                <label className="block text-white/80 text-sm font-medium mb-2">Google Drive / File Sharing</label>
                <input
                  type="url"
                  value={formData.urls.googleDrive}
                  onChange={(e) => handleUrlChange('googleDrive', e.target.value)}
                  className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://drive.google.com/..."
                />
              </div>
            </div>

            <div>
              <label className="block text-white/80 text-sm font-medium mb-2">Figma Design URL</label>
              <input
                type="url"
                value={formData.urls.figma}
                onChange={(e) => handleUrlChange('figma', e.target.value)}
                className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://figma.com/..."
              />
            </div>

            {/* Additional URLs */}
            {formData.additionalUrls.map((urlField, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-2 items-end">
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">URL Label</label>
                  <input
                    type="text"
                    value={urlField.label}
                    onChange={(e) => updateAdditionalUrl(index, 'label', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Demo Video"
                  />
                </div>
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">URL</label>
                  <input
                    type="url"
                    value={urlField.url}
                    onChange={(e) => updateAdditionalUrl(index, 'url', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://..."
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeAdditionalUrl(index)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors"
                >
                  Remove
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addAdditionalUrl}
              className="bg-blue-600/50 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors border border-blue-400/30"
            >
              + Add URL Field
            </button>
          </div>

          {/* File Uploads */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">File Uploads (Optional)</h3>

            {formData.files.map((fileField, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-2 items-end">
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">File Label</label>
                  <input
                    type="text"
                    value={fileField.label}
                    onChange={(e) => updateFileUpload(index, 'label', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Project Documentation"
                  />
                </div>
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">File</label>
                  <input
                    type="file"
                    onChange={(e) => updateFileUpload(index, 'file', e.target.files[0])}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeFileUpload(index)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors"
                >
                  Remove
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addFileUpload}
              className="bg-purple-600/50 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors border border-purple-400/30"
            >
              + Add File Upload
            </button>
          </div>

          {/* Custom Fields */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">Custom Fields</h3>

            {formData.customFields.map((customField, index) => (
              <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-2 items-end">
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Field Label</label>
                  <input
                    type="text"
                    value={customField.label}
                    onChange={(e) => updateCustomField(index, 'label', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Tech Stack"
                  />
                </div>
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Field Type</label>
                  <select
                    value={customField.type}
                    onChange={(e) => updateCustomField(index, 'type', e.target.value)}
                    className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="text">Text</option>
                    <option value="textarea">Textarea</option>
                    <option value="number">Number</option>
                    <option value="url">URL</option>
                    <option value="email">Email</option>
                  </select>
                </div>
                <div>
                  <label className="block text-white/80 text-sm font-medium mb-2">Value</label>
                  {customField.type === 'textarea' ? (
                    <textarea
                      value={customField.value}
                      onChange={(e) => updateCustomField(index, 'value', e.target.value)}
                      className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                      rows={3}
                    />
                  ) : (
                    <input
                      type={customField.type}
                      value={customField.value}
                      onChange={(e) => updateCustomField(index, 'value', e.target.value)}
                      className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  )}
                </div>
                <button
                  type="button"
                  onClick={() => removeCustomField(index)}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors"
                >
                  Remove
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addCustomField}
              className="bg-green-600/50 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors border border-green-400/30"
            >
              + Add Custom Field
            </button>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 border border-white/30 text-white rounded-lg hover:bg-white/10 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || isExpired}
              className="px-8 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isExpired ? 'Submissions Closed' : isSubmitting ? 'Submitting...' : 'Submit Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RegistrationForm;
