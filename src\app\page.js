'use client';

import { useEffect, useState } from 'react';
import Timer from '../components/Timer';
import LogoLayout from '../components/LogoLayout';
import BackgroundAnimation from '../components/BackgroundAnimation';
import RegistrationForm from '../components/RegistrationForm';

export default function Home() {
  const [isSubmissionOpen, setIsSubmissionOpen] = useState(false);
  const [isTimerExpired, setIsTimerExpired] = useState(false);

  // Request notification permission on component mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const handleOpenSubmission = () => {
    if (!isTimerExpired) {
      setIsSubmissionOpen(true);
    }
  };

  const handleTimerExpired = () => {
    setIsTimerExpired(true);
  };

  const handleCloseSubmission = () => {
    setIsSubmissionOpen(false);
  };
  const handleTimeUp = () => {
    // Play a simple beep sound using Web Audio API
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 800; // 800 Hz tone
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 1);
    } catch (error) {
      console.log('Audio notification not available');
    }

    // Browser notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Hackathon Timer', {
        body: 'Time is up!',
        icon: '/ncshack_logo.png'
      });
    }

    console.log('Time is up!');
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 grid-background">
      {/* Background Animation Layer */}
      <BackgroundAnimation />

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header with Logo Layout */}
        <header className="pt-8 pb-4">
          <LogoLayout />
        </header>

        {/* Main Timer Section */}
        <main className="flex-1 flex items-center justify-center px-4">
          <Timer
            initialMinutes={60}
            onRegister={handleOpenSubmission}
            onExpired={handleTimerExpired}
          />
        </main>

        {/* Footer */}
        <footer className="pb-8 text-center">
          <div className="text-white/60 text-sm">
            <p>Hackathon Timer • Built with Next.js & Tailwind CSS</p>
          </div>
        </footer>
      </div>

      {/* Admin Link */}
      <div className="absolute top-4 right-4 z-10">
        <a
          href="/login"
          className="bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 text-white px-4 py-2 rounded-lg text-sm transition-colors"
        >
          Admin
        </a>
      </div>

      {/* Overlay for better contrast */}
      <div className="absolute inset-0 bg-black/20 pointer-events-none z-5"></div>

      {/* Submission Form Modal */}
      <RegistrationForm
        isOpen={isSubmissionOpen}
        onClose={handleCloseSubmission}
        isExpired={isTimerExpired}
      />
    </div>
  );
}
