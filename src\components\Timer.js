'use client';

import { useState, useEffect, useCallback } from 'react';
import { getTimerConfig } from '../lib/supabase';

const Timer = ({ initialMinutes = 60, onRegister, onExpired }) => {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60); // Convert to seconds
  const [isRunning, setIsRunning] = useState(true); // Auto-start the timer
  const [isExpired, setIsExpired] = useState(false);
  const [timerConfig, setTimerConfig] = useState(null);

  // Load timer configuration from Supabase
  useEffect(() => {
    const loadTimerConfig = async () => {
      try {
        const { data } = await getTimerConfig();
        if (data) {
          setTimerConfig(data);
          const endTime = new Date(data.end_time);
          const now = new Date();
          const remainingSeconds = Math.max(0, Math.floor((endTime - now) / 1000));

          setTimeLeft(remainingSeconds);
          if (remainingSeconds === 0) {
            setIsExpired(true);
            setIsRunning(false);
            if (onExpired) onExpired();
          }
        }
      } catch (error) {
        console.error('Error loading timer config:', error);
      }
    };

    loadTimerConfig();
  }, [onExpired]);

  useEffect(() => {
    let interval = null;

    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => {
          if (time <= 1) {
            setIsRunning(false);
            setIsExpired(true);
            if (onExpired) onExpired();
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    } else if (!isRunning) {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [isRunning, timeLeft]);

  const formatTime = useCallback((seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const getTimeColor = () => {
    if (timeLeft <= 300) return 'animate-flash-red'; // Last 5 minutes - flashing red
    if (timeLeft <= 600) return 'text-yellow-400'; // Last 10 minutes
    return 'text-white';
  };

  const getTimerContainerClass = () => {
    if (timeLeft <= 300) return 'animate-pulse-glow'; // Add glow effect for last 5 minutes
    return '';
  };

  return (
    <div className="flex flex-col items-center space-y-8">
      {/* Timer Display */}
      <div className={`text-center ${getTimerContainerClass()}`}>
        <div className="text-white/80 text-lg sm:text-xl font-semibold mb-2 tracking-wide">
          TIME REMAINING
        </div>
        <div className={`text-5xl sm:text-7xl md:text-8xl lg:text-9xl font-black tracking-tight ${getTimeColor()} drop-shadow-2xl`}>
          {formatTime(timeLeft)}
        </div>
      </div>

      {/* Submissions Button or Expired Message */}
      <div className="flex justify-center mt-8">
        {isExpired ? (
          <div className="text-center">
            <div className="bg-red-600/20 border border-red-500/50 rounded-2xl px-12 py-6 mb-4">
              <h3 className="text-red-200 text-2xl font-bold mb-2">TIME'S UP!</h3>
              <p className="text-red-300">The submission period has ended.</p>
              <p className="text-red-300/80 text-sm mt-2">No new submissions are being accepted.</p>
            </div>
          </div>
        ) : (
          <button
            onClick={onRegister}
            className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-full font-bold text-xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105"
          >
            SUBMISSIONS
          </button>
        )}
      </div>
    </div>
  );
};

export default Timer;
