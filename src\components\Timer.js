'use client';

import { useState, useEffect, useCallback } from 'react';

const Timer = ({ initialMinutes = 60, onRegister }) => {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60); // Convert to seconds
  const [isRunning, setIsRunning] = useState(true); // Auto-start the timer

  useEffect(() => {
    let interval = null;

    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => {
          if (time <= 1) {
            setIsRunning(false);
            return 0;
          }
          return time - 1;
        });
      }, 1000);
    } else if (!isRunning) {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [isRunning, timeLeft]);

  const formatTime = useCallback((seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const getTimeColor = () => {
    if (timeLeft <= 300) return 'text-red-400'; // Last 5 minutes
    if (timeLeft <= 600) return 'text-yellow-400'; // Last 10 minutes
    return 'text-white';
  };

  return (
    <div className="flex flex-col items-center space-y-8">
      {/* Timer Display */}
      <div className="text-center">
        <div className="text-white/80 text-lg sm:text-xl font-semibold mb-2 tracking-wide">
          TIME REMAINING
        </div>
        <div className={`text-5xl sm:text-7xl md:text-8xl lg:text-9xl font-black tracking-tight ${getTimeColor()} drop-shadow-2xl`}>
          {formatTime(timeLeft)}
        </div>
      </div>

      {/* Register Button */}
      <div className="flex justify-center mt-8">
        <button
          onClick={onRegister}
          className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white px-12 py-4 rounded-full font-bold text-xl transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105"
        >
          SUBMISSIONS
        </button>
      </div>
    </div>
  );
};

export default Timer;
