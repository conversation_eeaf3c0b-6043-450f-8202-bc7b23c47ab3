'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  getCurrentUser, 
  checkAdminStatus, 
  signOut, 
  getAllSubmissions, 
  getTimerConfig, 
  updateTimerConfig 
} from '../../lib/supabase';

export default function AdminDashboard() {
  const [user, setUser] = useState(null);
  const [submissions, setSubmissions] = useState([]);
  const [timerConfig, setTimerConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('submissions');
  const [newEndTime, setNewEndTime] = useState('');
  const router = useRouter();

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        const { user } = await getCurrentUser();
        
        if (!user) {
          router.push('/login');
          return;
        }

        const { isAdmin } = await checkAdminStatus(user.id);
        
        if (!isAdmin) {
          router.push('/');
          return;
        }

        setUser(user);
        await loadSubmissions();
        await loadTimerConfig();
      } catch (error) {
        console.error('Auth check failed:', error);
        router.push('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndLoadData();
  }, [router]);

  const loadSubmissions = async () => {
    const { data, error } = await getAllSubmissions();
    if (data) {
      setSubmissions(data);
    } else if (error) {
      console.error('Error loading submissions:', error);
    }
  };

  const loadTimerConfig = async () => {
    const { data, error } = await getTimerConfig();
    if (data) {
      setTimerConfig(data);
      setNewEndTime(new Date(data.end_time).toISOString().slice(0, 16));
    } else if (error) {
      console.error('Error loading timer config:', error);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  const handleUpdateTimer = async (e) => {
    e.preventDefault();
    try {
      const endTime = new Date(newEndTime).toISOString();
      const { data, error } = await updateTimerConfig(endTime);
      
      if (error) {
        throw error;
      }

      setTimerConfig(data);
      alert('Timer updated successfully!');
    } catch (error) {
      console.error('Error updating timer:', error);
      alert('Error updating timer. Please try again.');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 grid-background flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 grid-background">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-md border-b border-white/20 p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold text-white">Hackathon Admin Dashboard</h1>
          <div className="flex items-center space-x-4">
            <span className="text-white/60">Welcome, {user?.email}</span>
            <button
              onClick={handleSignOut}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="max-w-7xl mx-auto p-4">
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => setActiveTab('submissions')}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              activeTab === 'submissions'
                ? 'bg-blue-600 text-white'
                : 'bg-white/10 text-white/70 hover:bg-white/20'
            }`}
          >
            Submissions ({submissions.length})
          </button>
          <button
            onClick={() => setActiveTab('timer')}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              activeTab === 'timer'
                ? 'bg-blue-600 text-white'
                : 'bg-white/10 text-white/70 hover:bg-white/20'
            }`}
          >
            Timer Control
          </button>
        </div>

        {/* Submissions Tab */}
        {activeTab === 'submissions' && (
          <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-white">Project Submissions</h2>
              <button
                onClick={loadSubmissions}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Refresh
              </button>
            </div>

            {submissions.length === 0 ? (
              <div className="text-center text-white/60 py-8">
                No submissions yet.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-white">
                  <thead>
                    <tr className="border-b border-white/20">
                      <th className="text-left p-3">Team Name</th>
                      <th className="text-left p-3">Leader</th>
                      <th className="text-left p-3">Email</th>
                      <th className="text-left p-3">University</th>
                      <th className="text-left p-3">Submitted</th>
                      <th className="text-left p-3">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {submissions.map((submission) => (
                      <tr key={submission.id} className="border-b border-white/10 hover:bg-white/5">
                        <td className="p-3 font-medium">{submission.team_name}</td>
                        <td className="p-3">{submission.team_leader}</td>
                        <td className="p-3">{submission.email}</td>
                        <td className="p-3">{submission.university}</td>
                        <td className="p-3">{formatDate(submission.submitted_at)}</td>
                        <td className="p-3">
                          <button
                            onClick={() => {
                              // TODO: Implement view details modal
                              console.log('View submission:', submission);
                            }}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
                          >
                            View Details
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Timer Control Tab */}
        {activeTab === 'timer' && (
          <div className="bg-slate-900/95 backdrop-blur-md border border-white/20 rounded-2xl p-6">
            <h2 className="text-xl font-bold text-white mb-6">Timer Configuration</h2>
            
            {timerConfig && (
              <div className="mb-6 p-4 bg-white/10 rounded-lg">
                <h3 className="text-white font-medium mb-2">Current Timer</h3>
                <p className="text-white/70">End Time: {formatDate(timerConfig.end_time)}</p>
                <p className="text-white/70">Status: {timerConfig.is_active ? 'Active' : 'Inactive'}</p>
              </div>
            )}

            <form onSubmit={handleUpdateTimer} className="space-y-4">
              <div>
                <label className="block text-white/80 text-sm font-medium mb-2">
                  Set New End Time
                </label>
                <input
                  type="datetime-local"
                  required
                  value={newEndTime}
                  onChange={(e) => setNewEndTime(e.target.value)}
                  className="w-full bg-white/10 border border-white/30 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Update Timer
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}
