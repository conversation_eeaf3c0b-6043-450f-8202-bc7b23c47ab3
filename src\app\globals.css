@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations for the hackathon timer */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(5deg);
  }
  50% {
    transform: translateY(-20px) rotate(0deg);
  }
  75% {
    transform: translateY(-10px) rotate(-5deg);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-15px) translateX(10px) rotate(3deg) scale(1.02);
  }
  50% {
    transform: translateY(-30px) translateX(0px) rotate(0deg) scale(1.05);
  }
  75% {
    transform: translateY(-15px) translateX(-10px) rotate(-3deg) scale(1.02);
  }
}

@keyframes float-reverse {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(180deg) scale(1);
  }
  25% {
    transform: translateY(10px) translateX(-8px) rotate(183deg) scale(0.98);
  }
  50% {
    transform: translateY(20px) translateX(0px) rotate(180deg) scale(0.95);
  }
  75% {
    transform: translateY(10px) translateX(8px) rotate(177deg) scale(0.98);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

@keyframes flash-red {
  0%, 100% {
    color: rgb(248 113 113); /* red-400 */
    text-shadow: 0 0 20px rgba(248, 113, 113, 0.8);
  }
  50% {
    color: rgb(239 68 68); /* red-500 */
    text-shadow: 0 0 30px rgba(239, 68, 68, 1);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(248, 113, 113, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(239, 68, 68, 0.8);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
}

.animate-float-slow {
  animation: float-slow 15s ease-in-out infinite;
  will-change: transform;
}

.animate-float-reverse {
  animation: float-reverse 12s ease-in-out infinite;
  will-change: transform;
}

.animate-twinkle {
  animation: twinkle 3s ease-in-out infinite;
  will-change: opacity, transform;
}

.animate-flash-red {
  animation: flash-red 1s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 1s ease-in-out infinite;
}

/* Performance optimizations */
.timer-container {
  transform: translateZ(0);
  backface-visibility: hidden;
}

.background-element {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Grid pattern background */
.grid-background {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  background-position: 0 0, 0 0;
}
