import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database schema and helper functions
export const createTables = async () => {
  // This would typically be done via Supabase dashboard or migrations
  // Including here for reference of the expected schema
  
  const submissions_table = `
    CREATE TABLE IF NOT EXISTS submissions (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      team_name VARCHAR NOT NULL,
      team_leader VARCHAR NOT NULL,
      email VARCHAR NOT NULL,
      phone VARCHAR,
      university VARCHAR,
      members JSONB DEFAULT '[]',
      experience VARCHAR,
      project_description TEXT NOT NULL,
      urls JSONB DEFAULT '{}',
      files JSONB DEFAULT '[]',
      custom_fields JSONB DEFAULT '{}',
      submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  const timer_config_table = `
    CREATE TABLE IF NOT EXISTS timer_config (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      end_time TIMESTAMP WITH TIME ZONE NOT NULL,
      is_active BOOLEAN DEFAULT true,
      created_by UUID REFERENCES auth.users(id),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  const admin_users_table = `
    CREATE TABLE IF NOT EXISTS admin_users (
      id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id UUID REFERENCES auth.users(id) UNIQUE,
      email VARCHAR NOT NULL,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;

  // Row Level Security policies would be set up in Supabase dashboard
  console.log('Database schema defined:', {
    submissions_table,
    timer_config_table,
    admin_users_table
  });
};

// Submission operations
export const submitProject = async (formData) => {
  try {
    const { data, error } = await supabase
      .from('submissions')
      .insert([formData])
      .select();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error submitting project:', error);
    return { data: null, error };
  }
};

export const getAllSubmissions = async () => {
  try {
    const { data, error } = await supabase
      .from('submissions')
      .select('*')
      .order('submitted_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching submissions:', error);
    return { data: null, error };
  }
};

// Timer operations
export const getTimerConfig = async () => {
  try {
    const { data, error } = await supabase
      .from('timer_config')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "no rows returned"
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching timer config:', error);
    return { data: null, error };
  }
};

export const updateTimerConfig = async (endTime) => {
  try {
    // Deactivate existing timers
    await supabase
      .from('timer_config')
      .update({ is_active: false })
      .eq('is_active', true);

    // Create new timer config
    const { data, error } = await supabase
      .from('timer_config')
      .insert([{
        end_time: endTime,
        is_active: true
      }])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating timer config:', error);
    return { data: null, error };
  }
};

// Auth operations
export const signInWithEmail = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error };
  }
};

export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return { user, error: null };
  } catch (error) {
    console.error('Error getting current user:', error);
    return { user: null, error };
  }
};

export const checkAdminStatus = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return { isAdmin: !!data, error: null };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return { isAdmin: false, error };
  }
};
